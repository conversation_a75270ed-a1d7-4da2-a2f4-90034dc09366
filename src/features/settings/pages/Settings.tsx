import { useState } from 'react'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import Card from '../../../common/components/card/Card'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import LanguageSettings from '../components/LanguageSettings'

/**
 * Renders settings page
 */
const Settings = () => {
  const t = useTranslate()
  const [languageOpen, setCurrentLanguage] = useState(false)

  return (
    <Layout
      containerWidth={'small'}
      pageTitle={
        languageOpen
          ? t('LANGUAGE_SETTINGS.PAGE_TITLE')
          : t('SETTINGS.PAGE_TITLE')
      }
      navigateTo={PUBLIC.GO_TO_PREV_PAGE}
      bottomSection={
        // biome-ignore lint/complexity/noUselessFragments: <Needed here>
        <>
          {languageOpen && (
            <NavigationButtons
              hideBackButton
              secondButtonLabel={t('SAVE_BUTTON_LABEL')}
              onClickSecond={() => setCurrentLanguage(false)}
            />
          )}
        </>
      }
    >
      {!languageOpen && (
        <Card
          title={t('LANGUAGE_SETTINGS.PAGE_TITLE')}
          variant="gray-dirty"
          showArrow
          headerImage={ASSET.iconaccountmenulanuae}
          interactEnabled
          onClick={() => setCurrentLanguage(!languageOpen)}
        />
      )}

      {languageOpen && <LanguageSettings />}
    </Layout>
  )
}

export default Settings
