import Card from '../../../common/components/card/Card'
import i18n, { languages } from '../../../config/i18n'
import style from '../style/LanguageSettings.module.scss'

/**
 * Renders language settings
 */
const LanguageSettings = () => {
  return (
    <section className={style['language-settings__container']}>
      {languages.map((language) => {
        return (
          <Card
            key={language.value}
            title={language.fullName}
            subTitle={language.value}
            variant="gray-dirty"
            interactEnabled
            headerImage={language.icon}
            onClick={() => i18n.changeLanguage(language.value)}
            alert={language.value === i18n.language ? 'completed' : undefined}
          />
        )
      })}
    </section>
  )
}

export default LanguageSettings
