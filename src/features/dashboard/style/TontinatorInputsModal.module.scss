/* stylelint-disable */
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

/** @define tontinator-inputs-modal */
.tontinator-inputs-modal {
    z-index: 9999999999999;

    &__buttons-wrapper {
        @include mixins.flex-layout(column, $align-items: center, $justify-content: space-between, $gap: 0.5rem);
    }

    &__content-wrapper {
        padding: 0;
        height: 100vh;
        width: 100vw;
        max-width: unset;
        max-height: unset;
        border-radius: 0;
    }

    &__content {
        margin-top: 0;
        height: 100vh;
        width: 100vw;
    }

    &__public-tontinator-page__input-layout {
        padding: 2rem 1rem;
    }

    @media only screen and (max-width: variables.$mobile-devices) {
        &__public-tontinator-page__input-layout {
            padding: 1rem;
        }

        &__buttons-wrapper {
            padding: 1rem;
        }
    }
}