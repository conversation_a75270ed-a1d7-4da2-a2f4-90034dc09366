@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

.public-tontinator-page {
  @include mixins.flex-layout(column, none, none);

  &__chart-layout {
    width: 50%;
    // Makes sure the top callout is not cut
    margin-top: 25px;
  }

  &__back-mobile {
    display: none;
  }

  &__tontinator-container {
    @include mixins.flex-layout($align-items: flex-start);
    gap: 3.125rem;
  }

  &__bottom-cta-container {
    @include mixins.flex-layout(column);
    gap: 1.25rem;
  }

  &__slider-group {
    @include mixins.no-user-select;
    margin-bottom: 0.625rem;
  }

  &__input-layout {
    padding-right: 1rem;
    width: 400px;

    &--full {
      height: 700px;
      overflow: scroll;
    }
  }

  &__input-layout--no-group {
    max-width: 350px;
    flex-basis: 30%;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    gap: 5px;

    &__back-mobile {
      margin-top: 20px;
      display: block;
      height: 20px;
      width: 20px;
      filter: invert(0%) sepia(0%) saturate(12%) hue-rotate(257deg) brightness(100%) contrast(104%);
    }

    &__input-layout {
      max-width: 100%;
      padding: 1rem;
      padding-top: 2rem;
      height: 100%;

      &>main {
        margin-bottom: 15px;

        &>section {
          margin-bottom: 15px;
        }
      }
    }

    &__input-layout--no-group {
      max-width: 100%;
    }

    &__chart-layout {
      width: 100%;
      // Makes sure the top call out on phones is not cutout but the screen
      margin-top: 15px;
    }
  }
}