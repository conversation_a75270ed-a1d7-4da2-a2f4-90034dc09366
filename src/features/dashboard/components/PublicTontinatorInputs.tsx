import InputGroup from '../../../common/components/InputGroup'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { isLite } from '../../../config/lite'
import style from '../style/PublicTontinatorPage.module.scss'
import { PublicTontinatorInputsProps } from '../types/PublicTontinatorInptus.types'
import TontinatorInputs from './TontinatorInputsV2'

/**
 * Extended TontinatorInputs with `SelectSex` component
 */
const PublicTontinatorInputs = ({
  incomeForecastParams,
  setIncomeForecastParams,
  comparison,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  extendYellow,
  extendBlue,
  extendDefault,
  propsForDefaultLayout,
  propsForBlueLayout,
  propsForYellowLayout,
}: PublicTontinatorInputsProps) => {
  const t = useTranslate()

  return (
    <main
      className={
        style[`public-tontinator-page__input-layout${!isLite ? '--full' : ''}`]
      }
    >
      {comparison ? (
        // Sliders for the comparison page
        <>
          <InputGroup
            noStyle
            groupLabel={t('PLAN1_INPUT_GROUP')}
            variant="bordered"
            borderColor="light-blue"
          >
            {extendBlue}

            <TontinatorInputs
              {...propsForBlueLayout}
              formData={blueForecastParams}
              setFormData={setBlueForecastParams}
              hideCurrentAgeSlider
              skipComparePlanRangeAdjustment
              forceDisableRetAgeDecrement={
                blueForecastParams?.retirementAge?.age ===
                blueForecastParams?.contributionAge?.age
              }
              retirementSliderTestIds={{
                incrementButtonDataTestID:
                  UI_TEST_ID.retirementAgeIncrementButton,
                decrementButtonDataTestID:
                  UI_TEST_ID.retirementAgeDecrementButton,
                boxValueDataTestID: UI_TEST_ID.retirementAgeSliderBox,
              }}
              oneTimeContributionTestIds={{
                incrementButtonDataTestID: UI_TEST_ID.oneTimeIncrementButton,
                decrementButtonDataTestID: UI_TEST_ID.oneTimeDecrementButton,
                boxValueDataTestID: UI_TEST_ID.oneTimeSliderBox,
              }}
              trackRetirementSlider={{
                incBtnId: 'plan1_slider_increment_income_start_age',
                decBtnId: 'plan1_slider_decrement_income_start_age',
                incBtnBoxId: 'plan1_slider_increment_income_start_age_box',
                sliderId: 'plan1_slider_income_start_age',
              }}
              trackOneTimeContribution={{
                incBtnId: 'plan1_slider_increment_onetime_contribution',
                decBtnId: 'plan1_slider_decrement_onetime_contribution',
                incBtnBoxId: 'plan1_slider_increment_onetime_box',
                sliderId: 'plan1_slider_onetime_contribution',
              }}
              trackMonthlyContribution={{
                incBtnId: 'plan1_slider_increment_monthly_contribution',
                decBtnId: 'plan1_slider_decrement_monthly_contribution',
                incBtnBoxId: 'plan1_slider_increment_monthly_box',
                sliderId: 'plan1_slider_monthly_contribution',
              }}
            />
          </InputGroup>
          <InputGroup
            noStyle
            groupLabel={t('PLAN2_INPUT_GROUP')}
            variant="bordered"
            borderColor="yellow"
          >
            {extendYellow}

            <TontinatorInputs
              {...propsForYellowLayout}
              formData={yellowForecastParams}
              setFormData={setYellowForecastParams}
              sliderVariant={'yellow'}
              hideCurrentAgeSlider
              skipComparePlanRangeAdjustment
              forceDisableRetAgeDecrement={
                yellowForecastParams?.retirementAge?.age ===
                yellowForecastParams?.contributionAge?.age
              }
              retirementSliderTestIds={{
                incrementButtonDataTestID:
                  UI_TEST_ID.p2RetirementAgeIncrementButton,
                decrementButtonDataTestID:
                  UI_TEST_ID.p2RetirementAgeDecrementButton,
                boxValueDataTestID: UI_TEST_ID.p2RetirementAgeSliderBox,
              }}
              oneTimeContributionTestIds={{
                incrementButtonDataTestID: UI_TEST_ID.p2OneTimeIncrementButton,
                decrementButtonDataTestID: UI_TEST_ID.p2OneTimeDecrementButton,
                boxValueDataTestID: UI_TEST_ID.p2OneTimeRetirementSliderBox,
              }}
              trackRetirementSlider={{
                incBtnId: 'plan2_slider_increment_income_start_age',
                decBtnId: 'plan2_slider_decrement_income_start_age',
                incBtnBoxId: 'plan2_slider_increment_income_start_age_box',
                sliderId: 'plan2_slider_income_start_age',
              }}
              trackOneTimeContribution={{
                incBtnId: 'plan2_slider_increment_onetime_contribution',
                decBtnId: 'plan2_slider_decrement_onetime_contribution',
                incBtnBoxId: 'plan2_slider_increment_onetime_box',
                sliderId: 'plan2_slider_onetime_contribution',
              }}
              trackMonthlyContribution={{
                incBtnId: 'plan2_slider_increment_monthly_contribution',
                decBtnId: 'plan2_slider_decrement_monthly_contribution',
                incBtnBoxId: 'plan2_slider_increment_monthly_box',
                sliderId: 'plan2_slider_monthly_contribution',
              }}
            />
          </InputGroup>
        </>
      ) : (
        // Sliders for the TONTINATOR PAGE
        <section
          className={style['public-tontinator-page__input-layout--no-group']}
        >
          {extendDefault}

          <TontinatorInputs
            {...propsForDefaultLayout}
            formData={incomeForecastParams}
            setFormData={setIncomeForecastParams}
            trackCurrentAgeSlider={{
              incBtnId: 'tontinator_slider_increment_age',
              decBtnId: 'tontinator_slider_decrement_age',
              incBtnBoxId: 'tontinator_slider_increment_age_box',
              sliderId: 'tontinator_slider_age',
            }}
            trackRetirementSlider={{
              incBtnId: 'tontinator_slider_increment_income_start_age',
              decBtnId: 'tontinator_slider_decrement_income_start_age',
              incBtnBoxId: 'tontinator_slider_increment_income_start_age_box',
              sliderId: 'tontinator_slider_income_start_age',
            }}
            trackOneTimeContribution={{
              incBtnId: 'tontinator_slider_increment_onetime_contribution',
              decBtnId: 'tontinator_slider_decrement_onetime_contribution',
              incBtnBoxId: 'tontinator_slider_increment_onetime_box',
              sliderId: 'tontinator_slider_onetime_contribution',
            }}
            trackMonthlyContribution={{
              incBtnId: 'tontinator_slider_increment_monthly_contribution',
              decBtnId: 'tontinator_slider_decrement_monthly_contribution',
              incBtnBoxId: 'tontinator_slider_increment_monthly_box',
              sliderId: 'tontinator_slider_monthly_contribution',
            }}
            oneTimeContributionTestIds={{
              incrementButtonDataTestID: UI_TEST_ID.oneTimeIncrementButton,
              decrementButtonDataTestID: UI_TEST_ID.oneTimeDecrementButton,
              boxValueDataTestID: UI_TEST_ID.oneTimeSliderBox,
            }}
          />
        </section>
      )}
    </main>
  )
}

export default PublicTontinatorInputs
