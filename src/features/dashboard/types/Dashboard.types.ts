import { CardVariantType } from '../../../common/types/Card.types'
import {
  InvestmentStrategyId,
  ReducedParams,
  TontinatorParamsMode,
  TrackActivity,
  ValidationData,
} from '../../../common/types/CommonTypes.types'
import { IncomeStatsExtendedProps } from '../../../common/types/DataDisplay.types'

type BottomCtaLiteLayoutProps = {
  onClickMobileTopButtonOnly?: () => void
  hideAlternativeButton?: boolean
  onClickBack?: () => void
  onClickPlan1?: () => void
  onClickPlan2?: () => void
  isCompareOpen?: boolean
  onClickComparison?: () => void
  onClickSignUpButton: () => void
  setIsOpenSignInModal?: (isOpen: boolean) => void
  blueForecastParams: ReducedParams
  yellowForecastParams: ReducedParams
  isSliderPageOpen?: boolean
  hideClickableText?: boolean
  incomeForecastParams: ReducedParams
}

type CompareIncomeStatsProps = {
  plan1: IncomeStatsExtendedProps
  plan2?: IncomeStatsExtendedProps
  currency?: string
}

type ComparePlanButtonsProps = {
  onClickMobileTopButtonOnly?: () => void
  onClickPlan1?: () => void
  onClickPlan2?: () => void
  blueForecastParams?: ReducedParams
  yellowForecastParams?: ReducedParams
  isSliderPageOpen?: boolean
  hideAlternativeButton?: boolean
  isMobileOrTablet?: boolean
}

type CurrencyStatProps = {
  currency: string
  amount: number
  label: string
  disableAnimation?: boolean
  isLoading?: boolean
}

type GraphSwitchesProps = {
  breakevenLabel: string
  inflationLabel: string
  percentage: boolean
  handlePercentage: () => void
  breakeven: boolean
  handleBreakeven: () => void
  inflation: boolean
  handleInflation: () => void
  togglesVariant?: 'button'
  currency?: string
}

type InfoBannerUnverifiedDoBProps = {
  onClickClickableText: () => void
  infoBannerText?: string
  infoBannerClickableText: string
  trans?: JSX.Element
}

type InvStrategiesDropdownProps = {
  value: InvestmentStrategyId
  onChange: (value: InvestmentStrategyId) => void
  label?: string
  errorMessage?: ValidationData
  validatorFunction?: () => void
  placeholder?: string
  readOnly?: boolean
  dataTestID?: string
  className?: string
  trackActivity?: TrackActivity
}

type NextPayoutProps = {
  previousAmount: string
  nextAmount: string
  maxProgress: number
  currentProgress: number
  variant: CardVariantType
}

type OnboardingButtonsProps = {
  tontinatorButtonLabel: string
  tontinatorButtonOnClick: () => void
  signupButtonLabel: string
  signupButtonOnClick: () => void
  showSignupButton?: boolean
}

type ParamModesProps = {
  activeMode: TontinatorParamsMode
  onChange: (mode: TontinatorParamsMode) => void
}

export type {
  BottomCtaLiteLayoutProps,
  CompareIncomeStatsProps,
  ComparePlanButtonsProps,
  CurrencyStatProps,
  GraphSwitchesProps,
  InfoBannerUnverifiedDoBProps,
  InvStrategiesDropdownProps,
  NextPayoutProps,
  OnboardingButtonsProps,
  ParamModesProps,
}
