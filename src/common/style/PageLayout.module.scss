@use './abstracts/mixins';
@use './abstracts/variables';

$sun-blue-desktop: url('../../assets/icon-onboarding_blue-sun-desktop.svg');
$sun-yellow-desktop: url('../../assets/icon-onboarding_happy-sun-desktop.svg');

// Target height is 660px
// 660px H = mt:50px + mb:10px
// There are a lot of combinations that will help you achieve this target set by design

@mixin page-layout($overflow-y: hidden) {
  overflow-y: $overflow-y;
  padding: 0;
  @include mixins.flex-layout(column);
  @include mixins.hide-webkit-scrollbar(block);
}

@mixin page-container($width: 100%,
  $height: 600px,
  $overflow-y: scroll,
  $mt: 50px,
  $mb: 10px) {
  height: $height;
  width: $width;
  overflow-y: $overflow-y;
  overflow-x: hidden;
  margin-top: $mt;
  margin-bottom: $mb;
  @include mixins.hide-webkit-scrollbar(block);

  @media only screen and (max-width: variables.$mobile-devices) {
    padding: 0 variables.$mobile-spacing;
  }
}

/** @define page-layout */
.page-layout {
  @include page-layout;

  &--sun-bg {
    @include mixins.yellow-sun-bg-top-left($background: $sun-yellow-desktop);
    @include page-layout;
  }

  &--sun-blue-bg {
    @include mixins.yellow-sun-bg-top-left($background: $sun-blue-desktop);
    @include page-layout;
  }

  //This is the content inside the page
  //The content can scroll if specified
  &__container {
    @include page-container;

    &--nomt {
      margin-top: 0 !important;
    }

    &--mt-20 {
      margin-top: 20px !important;
    }

    &--auto {
      height: 100vh !important;

      &-5 {
        height: 85vh !important;
      }
    }

    // Only to be used with the Lite build of the app!
    &--lite-build {
      height: 100vh !important;
    }

    &--sh {
      height: 460px !important;
    }

    &--lh {
      height: 700px !important;
    }

    &--xlh {
      height: 780px !important;
    }

    &--medium {
      @include page-container($width: 43.75rem);
    }

    &--small {
      @include page-container($width: 25rem);
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    @include page-layout($overflow-y: scroll);

    &--sun-bg {
      background: none;
    }

    &--sun-blue-bg {
      background: none;
    }

    &__container {
      height: 95vh;
      @include page-container($overflow-y: hidden, $mt: 30px, $mb: 0);

      &--sh {
        height: auto;
      }

      &--lh {
        height: auto;
      }

      &--xlh {
        height: auto !important;
      }

      &--medium {
        @include page-container($width: 100%, $mb: 0);
      }

      &--small {
        @include page-container($width: 100%, $mb: 0);
      }
    }
  }
}