import { banking, uas } from 'MyTontineConfig'

export const createApi = ({
  uasBaseUrl,
  bankingBaseUrl,
}: {
  uasBaseUrl: string
  bankingBaseUrl: string
}) => {
  return {
    healthCheck: `${uasBaseUrl}/v1/health_check`,
    logout: `${uasBaseUrl}/user_account/logout`,
    logoutAllDevices: `${uasBaseUrl}/v1/all_auth_tokens`,
    checkSession: `${uasBaseUrl}/v1/auth_token/seconds_remaining`,
    refreshSession: `${uasBaseUrl}/v1/session/refresh`,
    savePin: `${uasBaseUrl}/v1/pin`,
    extendLoginSessionWithPin: `${uasBaseUrl}/v1/session/extend`,
    sendEmailMagicLinkNewTab: `${uasBaseUrl}/magic_login/send_email/new_tab`,
    loginMagicLinkNewTab: `${uasBaseUrl}/v1/magic_login/redeem_token`,
    getScanSessionToken: `${uasBaseUrl}/face_scan/session_token`,
    enrollFace: `${uasBaseUrl}/v1/face_scan/enroll`,
    authenticateFace: `${uasBaseUrl}/v1/face_scan/authenticate`,
    scanId: `${uasBaseUrl}/v1/id_scan`,
    register: `${uasBaseUrl}/user_account/create`,
    userDetails: `${uasBaseUrl}/v1/user_account/read`,
    editUserDetails: `${uasBaseUrl}/v2/user_account/edit`,
    saveFeedback: `${uasBaseUrl}/v1/user_account/feedback/create`,
    closeAccount: `${uasBaseUrl}/v1/user_account/close`,
    cancelClosingAccount: `${uasBaseUrl}/v1/user_account/cancel_closure`,
    addPhoneNumber: `${uasBaseUrl}/v1/phone_number/update`,
    verifyPhoneNumber: `${uasBaseUrl}/v1/phone_number/verify`,
    changePin: `${uasBaseUrl}/v1/pin/update`,
    forgotPin: `${uasBaseUrl}/v1/pin/forgot`,
    resetPin: `${uasBaseUrl}/v1/pin/reset`,
    getAgreement: `${uasBaseUrl}/v1/agreement`,
    signAgreement: `${uasBaseUrl}/v1/agreement/agree`,
    createReferralCode: `${uasBaseUrl}/v1/referral_code/create`,
    redeemReferralCode: `${uasBaseUrl}/v1/referral_code/redeem`,
    getReferralStats: `${uasBaseUrl}/v1/referral_code/stats`,
    readNextPayout: `${uasBaseUrl}/v1/payouts/own`,
    updatePayoutAccount: `${uasBaseUrl}/v1/payout_account/update`,
    deletePayoutAccount: `${uasBaseUrl}/v1/payout_account/delete`,
    readUserBankingInfo: `${uasBaseUrl}/v1/user/read/own`,
    readUserNominalBalance: `${uasBaseUrl}/v1/logical_tx/balance_history`,
    tontinatorForecast: `${bankingBaseUrl}/v2/payout_forecast/tontinator`,
    forecastRules: `${bankingBaseUrl}/v1/forecast_rules`,
    readDraftPlan: `${uasBaseUrl}/v1/plan/read/draft`,
    readInvestmentFormFields: `${uasBaseUrl}/v1/form`,
    saveFormProgress: `${uasBaseUrl}/v1/form/update`,
    submitForm: `${uasBaseUrl}/v1/form/submit`,
    resendVerificationEmail: `${uasBaseUrl}/user_account/resend_verify`,
    liteAuth: `${uasBaseUrl}/v1/user_account/verify`,
    ipGeoLocation: `${uasBaseUrl}/geolocation/ip`,
    updateLitePlan: `${uasBaseUrl}/v1/user_account/update_lite`,
    submitUserFeedback: `${uasBaseUrl}/v1/feedback/add`,
    uploadAddressDocument: `${uasBaseUrl}/user_account/address/upload`,
    submitAddressInfo: `${uasBaseUrl}/user_account/address/submit`,
  } as const
}

export const API = createApi({
  uasBaseUrl: uas.baseUrl,
  bankingBaseUrl: banking.baseUrl,
})
