declare module 'MyTontineConfig' {
  const environment: 'development' | 'staging' | 'production'
  const envColor: string
  const uas: {
    emailEnvironment: 'staging' | 'dev' | 'prod'
    baseUrl:
      | 'http://localhost:8081'
      | 'https://staging-api.mytontine.com'
      | 'https://api.mytontine.com'
  }

  const facetec: {
    PublicFaceScanEncryptionKey: string
    deviceKeyIdentifier: string
    production: boolean
  }

  const banking: {
    baseUrl:
      | 'http://localhost:8083'
      | 'https://staging-api.mytontine.com'
      | 'https://api.mytontine.com'
  }

  const sentry: object

  /**
   * Does in dev mod the API request returns 200, because it is mocked by the backend
   */
  const analyticsTrack: boolean

  const mixpanelProxy = 'https://mixpanel.mytontine.com'

  const withCredentials: boolean

  export {
    analyticsTrack,
    banking,
    envColor,
    environment,
    /**
     *@note This config will come from the backend when production ready!
     */
    facetec,
    mixpanelProxy,
    sentry,
    uas,
    withCredentials,
  }
}
