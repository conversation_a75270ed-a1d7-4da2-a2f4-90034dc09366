import { lazy } from 'react'
import Account from '../features/authentication/pages/Account'
import RejectedID from '../features/authentication/pages/RejectedID'
import FundPensionMenu from '../features/banking/components/FundPensionMenu'
import PayoutMenu from '../features/banking/components/PayoutMenu'
// Already lazy loaded within the root component
import PayoutDetails from '../features/banking/pages/PayoutDetails'
import StatementsPage from '../features/banking/pages/StatementsPage'
import MyTontineDashboard from '../features/dashboard/pages/MyTontineMobileDashboard'
import NominalBalancePage from '../features/dashboard/pages/NominalBalancePage'
import SandboxTontinatorPage from '../features/dashboard/pages/SandboxTontinatorPage'
import Rewards from '../features/referral/components/Rewards'
import {
  ACCOUNT,
  FUND_PENSION,
  MYTT_DASHBOARD,
  PAYOUT,
  PERSONAL_DETAILS,
  PRIVATE,
} from './Route'
import { PrivatePage } from './Route.type'
const AddressVerificationFlow = lazy(
  () => import('../features/authentication/pages/AddressVerificationFlow')
)
const UpdatePhoneNumber = lazy(
  () => import('../features/authentication/pages/UpdatePhoneNumber')
)
const CloseAccount = lazy(
  () => import('../features/authentication/pages/CloseAccount')
)
const PinSetupPage = lazy(
  () => import('../features/authentication/pages/PinSetupPage')
)
const InvestmentAccountOpening = lazy(
  () => import('../features/agreements/pages/InvestmentAccountOpening')
)
const ViewAgreements = lazy(
  () => import('../features/agreements/pages/ViewAgreements')
)
const Settings = lazy(() => import('../features/settings/pages/Settings'))
const PersonalDetails = lazy(
  () => import('../features/authentication/pages/PersonalDetails')
)

/**
 * Pages that are meant to be accessed by authenticated users. Each index page
 * has child pages that are nested under it.
 *
 * You can specify if a certain private route requires some conditions to pass:
 *
 * - `writeProtected` - only allows the page to be accessed if the user has
 *   `write` permissions
 * - `l1KycRequired` - only allows the page to be accessed if the user has
 *   completed L1 KYC
 *
 * Both flags are allowed to be present at same time, or you can just specify
 * one flag
 */
const PrivatePages: Array<PrivatePage> = [
  //MyTontine dashboard route route
  {
    rootPath: PRIVATE.MYTT_DASHBOARD,
    indexPage: <MyTontineDashboard />,
    childRoutes: [
      {
        path: MYTT_DASHBOARD.NOMINAL_BALANCE,
        page: <NominalBalancePage />,
      },
      {
        path: MYTT_DASHBOARD.FUNDED_PROGRESS,
        page: <MyTontineDashboard />,
      },
      {
        path: MYTT_DASHBOARD.SANDBOX_TONTINATOR,
        page: <SandboxTontinatorPage />,
      },
      {
        path: MYTT_DASHBOARD.DASHBOARD_REWARDS,
        page: <Rewards />,
      },
    ],
  },
  //Account section ROOT Route
  {
    rootPath: PRIVATE.ACCOUNT,
    indexPage: <Account />,
    childRoutes: [
      {
        path: ACCOUNT.PERSONAL_DETAILS,
        page: <PersonalDetails />,
      },
      {
        path: PERSONAL_DETAILS.REJECTED_ID,
        page: <RejectedID />,
      },
      {
        path: PERSONAL_DETAILS.PHONE_NUMBER,
        writeProtected: true,
        page: <UpdatePhoneNumber />,
      },
      {
        path: ACCOUNT.REWARDS,
        page: <Rewards />,
      },
      {
        path: ACCOUNT.PIN,
        writeProtected: true,
        page: <PinSetupPage />,
      },
      {
        path: ACCOUNT.CLOSE_ACCOUNT,
        writeProtected: true,
        page: <CloseAccount />,
      },
      {
        path: ACCOUNT.VIEW_AGREEMENTS,
        page: <ViewAgreements />,
      },
      {
        path: ACCOUNT.SETTINGS,
        page: <Settings />,
      },
      //FUND ROUTES
      {
        path: ACCOUNT.FUND_PENSION,
        kycRequired: {
          l2: true,
        },
        page: <FundPensionMenu />,
      },
      {
        path: FUND_PENSION.CONTRIBUTION_HISTORY,
        kycRequired: {
          l2: true,
        },
        page: <StatementsPage />,
      },
      {
        path: FUND_PENSION.INVESTMENT_ACCOUNT,
        kycRequired: {
          l2: true,
        },
        writeProtected: true,
        page: <InvestmentAccountOpening />,
      },
      //PAYOUT ROUTES
      {
        path: ACCOUNT.PAYOUT,
        kycRequired: {
          l2: true,
        },
        page: <PayoutMenu />,
      },
      {
        path: PAYOUT.SETUP,
        kycRequired: {
          l2: true,
        },
        page: <PayoutDetails />,
      },
      {
        path: ACCOUNT.ADDRESS_VERIFICATION,
        kycRequired: {
          l1: true,
        },
        writeProtected: true,
        page: <AddressVerificationFlow />,
      },
    ],
  },
]

export { PrivatePages }
