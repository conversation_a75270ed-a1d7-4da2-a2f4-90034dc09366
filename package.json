{"name": "mytontine-webapp", "version": "1.0.0", "description": "MyTontine webapp", "private": true, "type": "module", "scripts": {"dev": "vite", "serve": "vite preview", "build": "vite build", "type-check": "tsc --noEmit", "test-coverage:text": "nyc report --reporter text -t cypress/.nyc_output", "tc-report": "npm run type-check && type-coverage", "biome-check": "biome check", "format": "biome format --write", "format-check": "biome format", "lint": "biome lint", "lint:format": "npm run format-check && npm run lint-scss && npm run lint", "lint-scss": "stylelint src/**/*.scss", "cypress:run": "scripts/prepare.sh && INSTRUMENT_CODE=true cypress run -P . -C cypress/cypress.config.ts", "cypress:ct": "npm run cypress:run -- --component --spec \"cypress/component/components/**/*.cy.tsx\"", "cypress:ut": "npm run cypress:run -- --component --spec \"cypress/component/unit/*.cy.tsx\"", "cypress-open": "cypress open -P . -C cypress/cypress.config.ts", "cypress-open:l": "PORT=9000 npm run cypress-open", "cypress-headless": "npm run cypress:run -- --spec \"./cypress/e2e/!(lite-*).ts\" --headless && npm run test-coverage:text", "cypress-headless:l": "PORT=9000 npm run cypress:run -- --spec \"./cypress/e2e/lite-*.ts\"", "dev:mt": "scripts/prepare.sh && npm run lint:format && npm run type-check && scripts/dev-server.sh", "build:app": "scripts/prepare.sh && npm run tc-report && cd scripts && ./build.sh", "build-analyze": "INSTRUMENT_CODE=false vite-bundle-visualizer -i ./index.html -c ./vite.config.ts"}, "repository": {"type": "git", "url": "git+https://github.com/tontinetrust/tontine-gui.git"}, "author": "<EMAIL>", "license": "ISC", "bugs": {"url": "https://github.com/tontinetrust/tontine-gui/issues"}, "homepage": "https://github.com/tontinetrust/tontine-gui#readme", "dependencies": {"@intercom/messenger-js-sdk": "^0.0.14", "@sentry/react": "^9.36.0", "@sentry/tracing": "^7.120.3", "@xstate/react": "^5.0.5", "axios": "^1.10.0", "cypress-vite": "^1.6.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-chained-backend": "^4.6.2", "i18next-http-backend": "^3.0.2", "i18next-localstorage-backend": "^4.2.0", "i18next-locize-backend": "^7.0.4", "libphonenumber-js": "^1.12.9", "locize": "^4.0.14", "locize-lastused": "^4.0.2", "lottie-web": "^5.13.0", "mixpanel-browser": "^2.66.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.0", "react-router-dom": "^6.30.1", "react-share": "^5.2.2", "react-toastify": "^11.0.5", "xstate": "^5.20.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@cypress/code-coverage": "^3.14.0", "@cypress/react18": "^2.0.1", "@percy/cli": "^1.31.0", "@percy/cypress": "^3.1.6", "@sentry/netlify-build-plugin": "^1.1.1", "@sentry/vite-plugin": "^3.5.0", "@statelyai/inspect": "^0.4.0", "@types/d3": "^7.4.3", "@types/mixpanel-browser": "^2.60.0", "@types/mocha": "^10.0.10", "@types/react": "^18.3.23", "@vitejs/plugin-react": "^4.6.0", "cypress": "14.5.1", "globals": "^16.3.0", "netlify-cli": "^22.2.2", "nyc": "^17.1.0", "postcss-bem-linter": "^4.0.1", "prop-types": "^15.8.1", "regenerator-runtime": "^0.14.1", "sass": "^1.89.2", "stylelint": "^16.21.1", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-scss": "^6.12.1", "stylelint-selector-bem-pattern": "^4.0.1", "terser": "^5.43.1", "type-coverage": "^2.29.7", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-istanbul": "^7.0.0"}, "typeCoverage": {"ignoreFiles": ["facetec/**/*", "cypress/coverage", "cypress/.nyc_output", "*.json", "coverage-ts/**/*", "src/public/**/*"], "strict": true, "atLeast": 95, "debug": false}}