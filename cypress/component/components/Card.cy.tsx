import ExtendedContentCard from '../../../src/common/components/ExtendedContentCard'
import Card from '../../../src/common/components/card/Card'
import CardAlert from '../../../src/common/components/card/CardAlert'
import CardFooter from '../../../src/common/components/card/CardFooter'
import CardHeader from '../../../src/common/components/card/CardHeader'
import {
  CardRoundness,
  cardBodyFooterVariants,
  cardHeaderVariants,
  cardVariants,
} from '../../../src/common/types/Card.types'
import { TESTING_IDS } from '../../support/ui-component-ids'

const cardTitle = 'Test Card Title'

const cardClass = 'card'
const cardHeaderClass = `${cardClass}__header`
const cardBodyClass = `${cardClass}__body`

const baseCardVariants = Object.values(cardVariants)
const bodyFooterCardVariants = Object.values(cardBodyFooterVariants)
const headerCardVariants = Object.values(cardHeaderVariants)

const roundnessValues: Array<{
  roundness: CardRoundness['roundness']
  value: string
}> = [
  { roundness: 'off', value: '0px' },
  { roundness: 'rounded-sm', value: '3px' },
  { roundness: 'rounded', value: '5px' },
  { roundness: 'rounded-l', value: '10px' },
]

describe('Card component', () => {
  context('Variant Tests', () => {
    baseCardVariants.forEach((variant) => {
      it(`css class: ${cardClass} with variant: "${variant}" is correctly applied`, () => {
        cy.mount(<Card variant={variant} title={cardTitle} />)
        cy.getByDataID(TESTING_IDS.commonCard).should(
          'have.class',
          `${cardClass}--${variant}`
        )
      })
    })

    headerCardVariants.forEach((variant) => {
      it(`css class ${cardHeaderClass} with variant: "${variant}" is correctly applied`, () => {
        cy.mount(
          <Card
            variant={variant}
            alert={1}
            alertAndArrowPosition="start"
            title={cardTitle}
          />
        )
        cy.getByDataID(TESTING_IDS.cardHeader).should(
          'have.class',
          `${cardHeaderClass}--${variant}`
        )
      })
    })

    bodyFooterCardVariants.forEach((variant) => {
      it(`css class ${cardBodyClass} with variant: "${variant}" is correctly applied`, () => {
        cy.mount(<Card variant={variant} title={cardTitle} />)
        cy.getByDataID(TESTING_IDS.cardBody).should(
          'have.class',
          `${cardBodyClass}--${variant}`
        )
      })
    })
  })

  context('Roundness Tests', () => {
    roundnessValues.forEach(({ roundness, value }) => {
      it(`applies roundness style "${roundness}"`, () => {
        cy.mount(<Card roundness={roundness} title={cardTitle} />)
        cy.getByDataID(TESTING_IDS.commonCard)
          .should('have.class', `${cardClass}--${roundness}`)
          .should('have.css', 'border-radius', value)
      })
    })
  })

  context('Interactivity Tests', () => {
    it('renders correctly with active state', () => {
      cy.mount(<Card active title={cardTitle} />)
      cy.getByDataID(TESTING_IDS.commonCard).should(
        'have.class',
        `${cardClass}--active`
      )
    })

    it('renders correctly with disabled state, and not clickable', () => {
      cy.mount(
        <Card disabled onClick={cy.stub().as('onClick')} title={cardTitle} />
      )
      cy.getByDataID(TESTING_IDS.commonCard)
        .should('have.class', `${cardClass}--disabled`)
        .should('have.css', {
          'user-select': 'none',
          cursor: 'not-allowed',
          'pointer-events': 'none',
          opacity: '0.5',
          'box-shadow': '0px 0px 4px rgba(0, 0, 0, 0.25)',
        })
      cy.get('@onClick').should('not.have.been.called')
    })

    it('renders correctly with enable-interact state', () => {
      cy.mount(<Card interactEnabled title={cardTitle} />)
      cy.getByDataID(TESTING_IDS.commonCard).should(
        'have.class',
        `${cardClass}--enable-interact`
      )
    })
  })

  context('Misc Tests', () => {
    it('onClick works and applies styles correctly', () => {
      cy.mount(<Card onClick={cy.stub().as('onClick')} title={cardTitle} />)
      cy.getByDataID(TESTING_IDS.commonCard)
        .should('have.class', `${cardClass}--pointer`)
        .should('have.css', 'cursor', 'pointer')
        .click()
      cy.get('@onClick').should('have.been.called')
    })

    it('renders correctly with a header image', () => {
      cy.mount(
        <Card
          title={cardTitle}
          headerImage="headerImage.png"
          headerImageSize="large"
        />
      )
      cy.get(`.${cardClass}__image`)
        .should('have.attr', 'src', 'headerImage.png')
        .should('have.class', `${cardClass}__image--large`)
    })

    it('renders correctly with a CTA button in the footer', () => {
      cy.mount(
        <Card
          title={cardTitle}
          ctaButtonLabel="Click Me"
          ctaButtonVariant="primary"
          onClick={cy.stub().as('onClick')}
        />
      )
      cy.get('button').should('contain.text', 'Click Me').click()
      cy.get('@onClick').should('have.been.called')
    })
  })

  context('Alert and Arrow Tests', () => {
    it('renders correctly with an alert in the header or footer', () => {
      cy.log('Card with alert in header')
      cy.mount(
        <Card title={cardTitle} alert={5} alertAndArrowPosition="start" />
      )
      cy.getByDataID(TESTING_IDS.cardAlert).should('contain.text', '5')

      cy.log('Card with alert in footer')
      cy.mount(<Card title={cardTitle} alert={3} alertAndArrowPosition="end" />)
      cy.getByDataID(TESTING_IDS.cardAlert).should('contain.text', '3')

      cy.log('Card with no alert')
      cy.mount(<Card title={cardTitle} alert={0} alertAndArrowPosition="end" />)
      cy.getByDataID(TESTING_IDS.cardAlert).should('not.exist')
    })

    it('renders correctly with arrows in the header or footer and with arrow states', () => {
      cy.log('Card with default arrow')
      cy.mount(<Card title={cardTitle} showArrow />)
      cy.getByDataID(TESTING_IDS.cardArrow).should(
        'have.class',
        `${cardClass}__arrow`
      )

      cy.log('Card with hidden arrow')
      cy.mount(<Card title={cardTitle} showArrow arrowInvisible />)
      cy.getByDataID(TESTING_IDS.cardArrow).should(
        'have.class',
        `${cardClass}__arrow--hidden`
      )

      cy.log('Card with up arrow in header')
      cy.mount(
        <Card
          title={cardTitle}
          alertAndArrowPosition="start"
          showArrow
          rotateArrow="up"
        />
      )
      cy.getByDataID(TESTING_IDS.cardArrow).should(
        'have.class',
        `${cardClass}__arrow--up`
      )

      cy.log('Card with down arrow in footer')
      cy.mount(
        <Card
          title={cardTitle}
          alertAndArrowPosition="end"
          showArrow
          rotateArrow="down"
        />
      )
      cy.getByDataID(TESTING_IDS.cardArrow).should(
        'have.class',
        `${cardClass}__arrow--down`
      )
    })

    it('renders correctly without alert and arrow', () => {
      cy.log('Card with no alert and no arrow')
      cy.mount(<Card title={cardTitle} />)
      cy.getByDataID(TESTING_IDS.cardAlert).should('not.exist')
      cy.getByDataID(TESTING_IDS.cardArrow).should('not.exist')

      cy.log(`Don't render arrow and alert in header if position isn't 'start'`)
      cy.mount(
        <CardHeader alertAndArrowPosition="end" alert="completed" showArrow />
      )
      cy.getByDataID(TESTING_IDS.cardAlert).should('not.exist')
      cy.getByDataID(TESTING_IDS.cardArrow).should('not.exist')

      cy.log(`Don't render arrow and alert in footer if position isn't 'end'`)
      cy.mount(
        <CardFooter alertAndArrowPosition="start" alert="completed" showArrow />
      )
      cy.getByDataID(TESTING_IDS.cardAlert).should('not.exist')
      cy.getByDataID(TESTING_IDS.cardArrow).should('not.exist')
    })
  })
})

describe('CardAlert Component', () => {
  context('Numeric Alert Tests', () => {
    it('renders correctly with a positive number', () => {
      cy.mount(<CardAlert alert={5} />)
      cy.getByDataID(TESTING_IDS.cardAlert).should('contain.text', '5')
    })

    it('renders nothing with a value of 0', () => {
      cy.mount(<CardAlert alert={0} />)
      cy.getByDataID(TESTING_IDS.cardAlert).should('not.exist')
    })

    it('renders nothing with a negative number', () => {
      cy.mount(<CardAlert alert={-3} />)
      cy.getByDataID(TESTING_IDS.cardAlert).should('not.exist')
    })
  })
})

describe('ExtendedContentCard Component', () => {
  const TestChildComponent = () => <div>Test Child Component</div>

  it('renders the component with default props', () => {
    cy.mount(
      <ExtendedContentCard title={cardTitle}>
        <TestChildComponent />
      </ExtendedContentCard>
    )
    cy.getByDataID(TESTING_IDS.commonCardExtended)
      .should('exist')
      .should('have.class', `extendedCard--rounded`)
  })

  it('toggles expand state on click', () => {
    cy.mount(
      <ExtendedContentCard title={cardTitle}>
        <TestChildComponent />
      </ExtendedContentCard>
    )
    cy.getByDataID(TESTING_IDS.commonCardExtended).click()
    cy.getByDataID(TESTING_IDS.commonCardExtended).should(
      'contain',
      'Test Child Component'
    )
  })

  it('calls onClick handler and expands child when clicked', () => {
    cy.mount(
      <ExtendedContentCard onClick={cy.stub().as('onClick')} title={cardTitle}>
        <TestChildComponent />
      </ExtendedContentCard>
    )
    cy.getByDataID(TESTING_IDS.commonCard).click()
    cy.get('@onClick').should('have.been.called')
    cy.getByDataID(TESTING_IDS.commonCardExtended).should(
      'contain',
      'Test Child Component'
    )
  })

  it('does not expand when expandClickDisabled is true', () => {
    cy.mount(
      <ExtendedContentCard title={cardTitle} expandClickDisabled>
        <TestChildComponent />
      </ExtendedContentCard>
    )
    cy.getByDataID(TESTING_IDS.commonCardExtended).click()
    cy.getByDataID(TESTING_IDS.commonCardExtended).should(
      'not.contain',
      'Test Child Component'
    )
  })

  it('renders children when auto expanded', () => {
    cy.mount(
      <ExtendedContentCard title={cardTitle} autoExpand={true}>
        <TestChildComponent />
      </ExtendedContentCard>
    )
    cy.getByDataID(TESTING_IDS.commonCardExtended).should(
      'contain',
      'Test Child Component'
    )
  })
})
